from rest_framework import serializers
from core.models import StudentEnrollment, User, OrderItem, Offering
from api.lms.serializers.credential import LmsCredentialListSerializer


class LmsStudentEnrollmentUserSerializer(serializers.ModelSerializer):
    """Serializer for User model"""

    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "uid",
            "email",
            "full_name",
            "first_name",
            "last_name",
            "phone_number",
        ]
        read_only_fields = ["uid"]

    def get_full_name(self, obj):
        if obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        if obj.first_name:
            return obj.first_name
        if obj.last_name:
            return obj.last_name
        return None


class LmsStudentEnrollmentOfferingSerializer(serializers.ModelSerializer):
    """Serializer for Offering model"""

    class Meta:
        model = Offering
        fields = [
            "oid",
            "name",
            "long_name",
            "code_name",
        ]
        read_only_fields = ["oid"]


class LmsStudentEnrollmentOrderItemSerializer(serializers.ModelSerializer):
    """Serializer for OrderItem model"""

    class Meta:
        model = OrderItem
        fields = [
            "id",
            "order",
        ]
        read_only_fields = ["id"]


class LmsStudentEnrollmentBaseSerializer(serializers.ModelSerializer):
    """Base serializer for StudentEnrollment with common fields"""

    class Meta:
        model = StudentEnrollment
        fields = [
            "eid",
            "order_item",
            "user",
            "certificate_issued",
            "certificate",
            "is_active",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["eid", "created_at", "updated_at"]


class LmsStudentEnrollmentListSerializer(LmsStudentEnrollmentBaseSerializer):
    """Serializer for listing student enrollments with minimal fields"""

    user = LmsStudentEnrollmentUserSerializer(read_only=True)
    offering = serializers.SerializerMethodField()
    order_item = LmsStudentEnrollmentOrderItemSerializer(read_only=True)
    key = serializers.CharField(source="eid", read_only=True)

    def get_offering(self, obj):
        return LmsStudentEnrollmentOfferingSerializer(
            obj.order_item.offering, read_only=True
        ).data

    class Meta(LmsStudentEnrollmentBaseSerializer.Meta):
        fields = LmsStudentEnrollmentBaseSerializer.Meta.fields + ["offering", "key"]


class LmsStudentEnrollmentRetrieveSerializer(serializers.ModelSerializer):
    """Detailed serializer for retrieving a specific student enrollment"""

    user = LmsStudentEnrollmentUserSerializer(read_only=True)
    order_item = LmsStudentEnrollmentOrderItemSerializer(read_only=True)
    offering = serializers.SerializerMethodField()
    certificate = LmsCredentialListSerializer(read_only=True)

    def get_offering(self, obj):
        return LmsStudentEnrollmentOfferingSerializer(
            obj.order_item.offering, read_only=True
        ).data

    class Meta(LmsStudentEnrollmentBaseSerializer.Meta):
        fields = LmsStudentEnrollmentBaseSerializer.Meta.fields + ["offering"]


class LmsStudentEnrollmentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new student enrollments"""

    class Meta:
        model = StudentEnrollment
        fields = [
            "order_item",
            "user",
            "is_active",
        ]

    def validate(self, attrs):
        """Validate that user and order_item combination is unique"""
        order_item = attrs.get("order_item")
        user = attrs.get("user")

        if StudentEnrollment.objects.filter(order_item=order_item, user=user).exists():
            raise serializers.ValidationError(
                "A student enrollment already exists for this user and order item."
            )

        return attrs


class LmsStudentEnrollmentUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating student enrollments"""

    class Meta:
        model = StudentEnrollment
        fields = [
            "is_active",
            "certificate_issued",
        ]

    def validate_certificate_issued(self, value):
        """Validate certificate issuance logic"""
        if value and not self.instance.is_active:
            raise serializers.ValidationError(
                "Cannot issue certificate for inactive enrollment."
            )
        return value
