from typing import Dict, Any, Optional
from django.template.loader import render_to_string
from weasyprint import HTML, CSS
from django.conf import settings
import os


class DocumentGenerator:

    def __init__(self, template_name: str, css_files: list[str] = None):
        """
        Args:
        template_name: Name of the HTML template (must be in templates/documents/)
        css_files: List of CSS files to apply (must be in static/css/documents/)
        """
        self.template_name = f"documents/{template_name}"
        self.css_files = css_files or []

    def _get_css_paths(self) -> list[str]:
        """Gets the full paths of CSS files."""
        css_dir = os.path.join(settings.STATIC_ROOT, "css", "documents")
        return [os.path.join(css_dir, css_file) for css_file in self.css_files]

    def generate_pdf(self, context: Dict[str, Any] = None) -> bytes:
        # Render the HTML template with the context
        html_content = render_to_string(self.template_name, context or {})

        # Create WeasyPrint HTML Object
        html = HTML(string=html_content, base_url=settings.STATIC_ROOT)

        # Load CSS files
        css_files = [CSS(filename=css_path) for css_path in self._get_css_paths()]

        # Generar PDF
        return html.write_pdf(stylesheets=css_files)

    @classmethod
    def create_certificate(
        cls,
        student_name: str,
        program_name: str,
        duration: str,
        date: str,
        signatures: Dict[str, str],
    ) -> bytes:
        """
        Args:
            student_name: Student name
            program_name: Program name
            duration: Program duration
            date: Issue date
        """
        generator = cls(template_name="certificate.html", css_files=["certificate.css"])

        context = {
            "student_name": student_name,
            "program_name": program_name,
            "duration": duration,
            "date": date,
            "signatures": signatures,
        }

        return generator.generate_pdf(context)

    @classmethod
    def create_payment_receipt(
        cls,
        receipt_number: str,
        student_name: str,
        amount: float,
        concept: str,
        date: str,
        address: str,
    ) -> bytes:
        """
        Args:
            receipt_number: Receipt number
            student_name: Student name
            amount: Amount paid
            concept: Payment concept
            date: Date of payment
            address: Address
        """
        generator = cls(
            template_name="payment_receipt.html", css_files=["payment_receipt.css"]
        )

        context = {
            "receipt_number": receipt_number,
            "student_name": student_name,
            "amount": amount,
            "concept": concept,
            "date": date,
            "address": address,
        }

        return generator.generate_pdf(context)
