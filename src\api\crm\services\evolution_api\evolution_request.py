"""
Evolution API request utilities
"""

from django.conf import settings
import requests
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class EvolutionAPIError(Exception):
    """Custom exception for Evolution API errors"""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


def evolution_request(
    uri: str,
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 90,
) -> Dict[str, Any]:
    """
    Make HTTP request to Evolution API

    Args:
        uri: API endpoint URI (e.g., '/instance/create')
        method: HTTP method (GET, POST, PUT, DELETE)
        data: Request body data for POST/PUT requests
        headers: Additional headers to include
        timeout: Request timeout in seconds

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails or API returns an error
    """
    # Get credentials from environment variables
    api_key = getattr(settings, "EVOLUTION_API_ACCESS_TOKEN", None)
    api_host = getattr(settings, "EVOLUTION_API_HOST", None)

    if not api_key:
        raise EvolutionAPIError("EVOLUTION_API_KEY environment variable is not set")

    if not api_host:
        raise EvolutionAPIError("EVOLUTION_API_HOST environment variable is not set")

    # Prepare headers
    request_headers = {
        "apikey": api_key,
        "Content-Type": "application/json",
    }

    if headers:
        request_headers.update(headers)

    # Build full URL
    url = f"https://{api_host.rstrip('/')}{uri}"

    try:
        # Prepare request parameters
        request_params = {
            "method": method.upper(),
            "url": url,
            "headers": request_headers,
            "timeout": timeout,
        }

        # Add data for POST/PUT requests
        if method.upper() in ["POST", "PUT", "PATCH"] and data is not None:
            request_params["json"] = data

        logger.info(f"Making {method.upper()} request to Evolution API: {uri}")

        # Make the request
        response = requests.request(**request_params)

        # Check if request was successful
        if response.status_code >= 200 and response.status_code < 300:
            try:
                response_data = response.json()
                logger.info(f"Evolution API request successful: {uri}")
                return {
                    "success": True,
                    "data": response_data,
                    "status_code": response.status_code,
                }
            except ValueError:
                # Response is not JSON
                return {
                    "success": True,
                    "data": response.text,
                    "status_code": response.status_code,
                }
        else:
            error_message = (
                f"Evolution API request failed with status {response.status_code}"
            )
            logger.error(f"{error_message}: {uri}")

            try:
                error_data = response.json()
            except ValueError:
                error_data = {"error": response.text}

            raise EvolutionAPIError(
                message=error_message,
                status_code=response.status_code,
                response_data=error_data,
            )

    except requests.exceptions.RequestException as e:
        error_message = f"Network error while calling Evolution API: {str(e)}"
        logger.error(f"{error_message}: {uri}")
        raise EvolutionAPIError(message=error_message)
    except Exception as e:
        error_message = f"Unexpected error while calling Evolution API: {str(e)}"
        logger.error(f"{error_message}: {uri}")
        raise EvolutionAPIError(message=error_message)
