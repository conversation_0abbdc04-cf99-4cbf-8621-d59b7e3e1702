from rest_framework import serializers


class CertificateGenerationSerializer(serializers.Serializer):
    def validate(self, attrs):
        enrollment = self.context.get("enrollment")
        if not enrollment:
            raise serializers.ValidationError(
                "No se encontró la matrícula especificada"
            )
        if enrollment.certificate:
            raise serializers.ValidationError(
                "El estudiante ya tiene un certificado asignado"
            )
        return attrs
