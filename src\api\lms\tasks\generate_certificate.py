from celery import shared_task
from django.db import transaction
from django.utils import timezone
from django.conf import settings
from django.utils.formats import date_format
from django.utils.translation import activate, deactivate
from core.models import StudentEnrollment, File, Credential
from services.documents.document_generator import DocumentGenerator
from storage.minio import MinioStorage
from io import BytesIO
import uuid
import logging

logger = logging.getLogger(__name__)

TASK_STATES = {
    "PENDING": "Pendiente",
    "STARTED": "Iniciando generación del certificado",
    "GENERATING": "Generando PDF del certificado",
    "SAVING": "Guardando certificado en el sistema",
    "COMPLETED": "Certificado generado exitosamente",
    "FAILED": "Error en la generación del certificado",
}


@shared_task(bind=True)
def generate_student_certificate(self, enrollment_id):
    """
    Tarea asíncrona para generar el certificado de un estudiante.

    Args:
        enrollment_id: UUID del StudentEnrollment
    """
    try:
        # Actualizar estado de la tarea
        self.update_state(state="STARTED")

        enrollment = StudentEnrollment.objects.get(eid=enrollment_id)

        # Si ya tiene certificado, no hacer nada
        if enrollment.certificate:
            return {
                "status": "skipped",
                "message": "El estudiante ya tiene un certificado asignado",
            }

        with transaction.atomic():
            # Generar el PDF
            self.update_state(state="GENERATING")

            offering = enrollment.order_item.offering
            user = enrollment.user

            # Manejo de duración vacía
            duration = offering.duration if offering.duration else "No especificada"

            # Configurar locale para fecha en español y formatear
            activate("es")
            formatted_date = date_format(enrollment.created_at, "j \d\e F \d\e Y")
            deactivate()

            pdf_content = DocumentGenerator.create_certificate(
                student_name=f"{user.first_name} {user.last_name}",
                program_name=offering.name,
                duration=str(duration),
                date=formatted_date,
                signatures=settings.CERTIFICATE_SIGNATURES,
            )

            # Crear el archivo en el bucket
            self.update_state(state="SAVING")

            file_uuid = str(uuid.uuid4())
            object_name = f"certificates/{enrollment_id}/{file_uuid}.pdf"

            # Crear el registro File
            file = File.objects.create(
                bucket_name=settings.MINIO_STORAGE_MEDIA_BUCKET_NAME,
                object_name=object_name,
                name=f"certificate_{enrollment_id}.pdf",
            )

            # Subir el archivo al bucket usando MinioStorage
            pdf_bytes = BytesIO(pdf_content)
            storage = MinioStorage()
            storage.upload(
                bucket_name=settings.MINIO_STORAGE_MEDIA_BUCKET_NAME,
                object_name=object_name,
                data=pdf_bytes,
                length=len(pdf_content),
                content_type="application/pdf",
            )

            # Crear el credential y asociarlo al enrollment
            credential = Credential.objects.create(
                file=file, issued_at=timezone.now(), has_expiration=False
            )

            # Asociar el credential al enrollment
            enrollment.certificate = credential
            enrollment.certificate_issued = True
            enrollment.save()

            # Actualizar estado de la tarea
            self.update_state(state="COMPLETED")

            return {
                "status": "success",
                "enrollment_id": str(enrollment.eid),
                "credential_id": str(credential.cid),
                "file_id": str(file.fid),
            }

    except Exception as e:
        logger.error(
            f"Error generando certificado para enrollment {enrollment_id}: {str(e)}"
        )
        # Actualizar estado de la tarea
        self.update_state(
            state="FAILED", meta={"exc_type": type(e).__name__, "exc_message": str(e)}
        )
        raise
