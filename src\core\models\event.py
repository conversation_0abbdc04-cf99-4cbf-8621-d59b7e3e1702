import uuid
import shortuuid
from django.db import models
from django.utils import timezone
from core.models.base import AuditBaseModel
from django.contrib.postgres.fields import IntegerRangeField
from psycopg2.extras import NumericRange
from datetime import timedelta


class Event(AuditBaseModel):
    REMOTE_MODALITY = "remote"
    IN_PERSON_MODALITY = "in_person"

    PLANNING_STAGE = "planning"
    LAUNCHED_STAGE = "launched"
    ENROLLMENT_CLOSED_STAGE = "enrollment_closed"
    FINISHED_STAGE = "finished"

    MODALITY_CHOICES = [
        (REMOTE_MODALITY, "Remote"),
        (IN_PERSON_MODALITY, "In-Person"),
    ]
    WORKSHOP_TYPE = "workshop"
    WEBINAR_TYPE = "webinar"
    HANDS_OF_WORKSHOP_TYPE = "hands_of_workshop"

    STAGE_CHOICES = [
        (PLANNING_STAGE, "Planning"),
        (LAUNCHED_STAGE, "Launched"),
        (ENROLLMENT_CLOSED_STAGE, "Enrollment Closed"),
        (FINISHED_STAGE, "Finished"),
    ]
    TYPE_CHOICES = [
        (WORKSHOP_TYPE, "Workshop"),
        (WEBINAR_TYPE, "Webinar"),
        (HANDS_OF_WORKSHOP_TYPE, "Hands-on Workshop"),
    ]
    eid = models.UUIDField(
        primary_key=True,
        editable=False,
        default=uuid.uuid4,
        verbose_name="Event ID",
        help_text="Unique identifier for the event",
    )
    slug = models.SlugField(
        max_length=255,
        unique=True,
        blank=False,
        verbose_name="Slug",
        help_text="Unique slug for the event, used in URLs",
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Event Name",
    )
    description = models.TextField(
        blank=True,
        verbose_name="Description",
    )
    modality = models.CharField(
        max_length=24,
        choices=MODALITY_CHOICES,
        default=REMOTE_MODALITY,
        verbose_name="Modality",
    )
    type = models.CharField(
        max_length=24,
        choices=TYPE_CHOICES,
        default=WORKSHOP_TYPE,
        verbose_name="Type",
    )
    location = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Location",
    )
    offering = models.ForeignKey(
        "Offering",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="events",
        verbose_name="Offering",
    )
    instructor = models.ForeignKey(
        "Instructor",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="events",
        verbose_name="Instructor",
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.0,
        verbose_name="Price",
    )
    thumbnail = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="events_thumbnail",
        blank=True,
        null=True,
        verbose_name="Thumbnail",
    )
    cover_image = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="events_cover_image",
        blank=True,
        null=True,
        verbose_name="Cover Image",
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Event"
        verbose_name_plural = "Events"


class EventSchedule(AuditBaseModel):
    """
    Event Schedule model to manage the schedule of events.
    """

    esid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        unique=True,
        verbose_name="Event Schedule ID",
    )
    short_esid = models.CharField(
        max_length=8,
        unique=True,
        blank=False,
        null=False,
        editable=False,
        verbose_name="Short Event Schedule ID",
        help_text="Short unique identifier for URLs and display",
    )
    event = models.ForeignKey(
        Event,
        on_delete=models.CASCADE,
        related_name="schedules",
        verbose_name="Event",
        blank=False,
        null=False,
        help_text="The event to which this schedule belongs",
    )
    is_general = models.BooleanField(
        default=False,
        verbose_name="Is General",
        help_text="Indicates if the schedule is general or specific to a partnership",
    )
    start_date = models.DateTimeField(
        default=timezone.now,
        verbose_name="Start Date",
    )
    end_date = models.DateTimeField(
        default=timezone.now,
        verbose_name="End Date",
    )
    partnerships = models.ManyToManyField(
        "Partnership",
        related_name="event_schedules",
        verbose_name="Partnerships",
        blank=True,
        help_text="Partnerships associated with this event schedule",
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Event Schedule Name",
        default="",
        help_text="Name of the event schedule",
    )
    description = models.TextField(
        blank=True,
        verbose_name="Event Schedule Description",
        help_text="A brief description of the event schedule",
    )
    stage = models.CharField(
        max_length=24,
        choices=Event.STAGE_CHOICES,
        default=Event.PLANNING_STAGE,
        verbose_name="Stage",
    )
    modality = models.CharField(
        max_length=24,
        choices=Event.MODALITY_CHOICES,
        default=Event.REMOTE_MODALITY,
        verbose_name="Modality",
    )

    location = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Location",
    )

    instructor = models.ForeignKey(
        "Instructor",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="event_schedules",
        verbose_name="Instructor",
    )

    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.0,
        verbose_name="Price",
    )
    thumbnail = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="event_schedules_thumbnail",
        blank=True,
        null=True,
        verbose_name="Thumbnail",
    )
    cover_image = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="event_schedules_cover_image",
        blank=True,
        null=True,
        verbose_name="Cover Image",
    )

    enrollments = models.ManyToManyField(
        "core.User",
        through="EventScheduleEnrollment",
        through_fields=("event_schedule", "user"),
        blank=True,
        related_name="enrolled_event_schedules",
        verbose_name="Enrollments",
        help_text="Users enrolled in this event schedule",
    )
    agenda = models.JSONField(
        blank=True,
        null=True,
        verbose_name="Agenda",
    )

    ext_event_id = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name="External Event ID",
        help_text="External identifier for the event schedule",
    )
    ext_event_link = models.URLField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="External Event Link",
        help_text="Link to the external event schedule",
    )
    ext_event_reference = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="External Event Reference",
        help_text="Reference for the external event schedule",  # This would be for calendar Id
    )

    emails_reminder_auto = models.BooleanField(
        default=True,
        blank=True,
        verbose_name="Emails Reminder Auto",
        help_text=(
            "If enabled, email notifications for new enrollments in this event schedule are sent automatically and instantly. "
            "If disabled, you must configure 'Email Scheduled DateTime' to schedule when notifications are sent."
        ),
    )

    # Notification scheduling fields
    scheduled_datetime_email = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Email Scheduled DateTime",
        help_text="When to start sending email notifications for this event schedule",
    )
    scheduled_datetime_whatsapp = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="WhatsApp Scheduled DateTime",
        help_text="When to start sending WhatsApp notifications for this event schedule",
    )

    # WhatsApp template for notifications
    whatsapp_template = models.ForeignKey(
        "Template",
        on_delete=models.SET_NULL,
        related_name="event_schedules_whatsapp",
        verbose_name="WhatsApp Template",
        null=True,
        blank=True,
        help_text="Template for WhatsApp notifications (uses ext_reference as flow ID)",
    )

    whatsapp_delay_range = IntegerRangeField(
        null=True,
        blank=True,
        verbose_name="WhatsApp Delay Range (in seconds)",
        help_text="Range of delay before sending WhatsApp notifications [min-max]",
    )

    is_whatsapp_active = models.BooleanField(
        null=True,
        blank=True,
        default=True,
        verbose_name="WhatsApp Notifications Active",
        help_text="Enable or disable WhatsApp notifications for this event schedule. If disabled, no WhatsApp invitations will be sent.",
    )

    def save(self, *args, **kwargs):
        # Generate default whatdsapp delay range
        if not self.whatsapp_delay_range and not self._state.adding:
            self.whatsapp_delay_range = NumericRange(5, 15, bounds="[]")

        # set default notification times to 30 minutes before start_date
        if self._state.adding or not self.pk:  # Only for new records
            if self.start_date:
                notification_time = self.start_date - timedelta(minutes=30)

                # Set email notification time if not provided
                if not self.scheduled_datetime_email:
                    self.scheduled_datetime_email = notification_time

                # Set WhatsApp notification time if not provided
                if not self.scheduled_datetime_whatsapp:
                    self.scheduled_datetime_whatsapp = notification_time

        if not self.short_esid:
            # Generate a unique short_esid of 10 characters
            # Use an alphabet without confusing characters (0, O, 1, I, l)
            shortuuid.set_alphabet("23456789abcdefghjkmnpqrstuvwxyz")
            self.short_esid = shortuuid.ShortUUID().random(length=8)

            # Check uniqueness and regenerate if necessary
            while EventSchedule.objects.filter(short_esid=self.short_esid).exists():
                self.short_esid = shortuuid.ShortUUID().random(length=8)

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.event.name} - {self.name}"

    class Meta:
        verbose_name = "Event Schedule"
        verbose_name_plural = "Event Schedules"
        ordering = ["start_date"]
        indexes = [
            models.Index(fields=["short_esid"], name="eventschedule_short_esid_idx"),
        ]


class EventScheduleEnrollment(AuditBaseModel):
    """
    Event Schedule Enrollment model to manage user enrollments in event schedules.
    """

    event_schedule = models.ForeignKey(
        EventSchedule,
        on_delete=models.CASCADE,
        related_name="enrollment_records",
        verbose_name="Event Schedule",
        blank=False,
        null=False,
        help_text="The event schedule in which the user is enrolled",
    )
    user = models.ForeignKey(
        "core.User",
        on_delete=models.SET_NULL,
        related_name="event_schedule_enrollments",
        verbose_name="User",
        blank=True,
        null=True,
        help_text="The user who is enrolled in the event schedule",
    )
    first_name = models.CharField(
        max_length=127,
        blank=True,
        null=True,
        verbose_name="First Name",
        help_text="User's first name, if available",
    )
    last_name = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name="Last Name",
        help_text="User's last name, if available",
    )
    email = models.EmailField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name="Email",
        help_text="User's email address, if available",
    )
    phone_number = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name="Phone Number",
        help_text="User's phone number, if available",
    )
    occupation = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name="Occupation",
        help_text="User's occupation, if available",
    )
    major = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name="University Major",
        help_text="User's university major, if available",
    )
    term = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        verbose_name="Term",
        help_text="User's academic term, if available",
    )
    university = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name="University",
        help_text="User's university, if available",
    )
    # Deprecated
    interests = models.JSONField(
        default=list,
        blank=True,
        verbose_name="Interests in Offerings and Events",
        help_text="User interests in offerings and events, stored as a JSON field",
    )
    user_interests = models.ManyToManyField(
        "Interest",
        blank=True,
        related_name="event_schedule_enrollments",
        verbose_name="User Interests",
        help_text="User interests in offerings and events",
    )
    ceu_to_apply = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name="CEU to Apply",
        help_text="User's target CEU to apply for, if the event is associated with a CEU preparation program.",
    )
    diffusion_channel = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name="Diffusion Channel",
        help_text="The channel through which the user learned about the event schedule",
    )
    has_contact = models.BooleanField(
        default=False,
        verbose_name="Has Contact",
        help_text="Indicates if the user has been contacted",
    )
    needs_conciliation = models.BooleanField(
        default=False,
        verbose_name="Needs Conciliation",
        help_text="Indicates if the user needs conciliation",
    )
    already_lead = models.BooleanField(
        default=False,
        verbose_name="Already Lead",
        help_text="Indicates if the user is already a lead for the offering associated with the event schedule.",
    )
    is_organic = models.BooleanField(
        default=True,
        verbose_name="Is Organic",
        help_text="Indicates if the user is organic or not (from paid campaigns)",
    )
    partnership = models.ForeignKey(
        "Partnership",
        on_delete=models.SET_NULL,
        related_name="event_schedule_enrollments",
        blank=True,
        null=True,
        verbose_name="Partnership",
        help_text="Partnership associated with the enrollment, if any",
    )


class Interest(AuditBaseModel):
    """
    Interest model for managing interests of user in offerings and events.
    """

    iid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )

    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Name",
    )

    code_name = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        default=None,
        verbose_name="Code Name",
        help_text="Code name of the academic offering. Used for internal purposes.",
    )

    class Meta:
        verbose_name = "Interest"
        verbose_name_plural = "Interests"

    def __str__(self):
        return self.name
