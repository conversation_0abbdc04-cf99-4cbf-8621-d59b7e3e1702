from rest_framework import viewsets, status
from rest_framework.response import Response
from celery.result import AsyncResult
from rest_framework.permissions import IsAuthenticated


class TaskViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def retrieve(self, request, pk=None):
        """
        Verifica el estado de una tarea asíncrona.
        """
        task = AsyncResult(pk)

        response = {
            "task_id": pk,
            "status": task.state,
        }

        if task.state == "SUCCESS":
            response.update(
                {"result": task.result, "message": "Tarea completada exitosamente"}
            )
        elif task.state == "FAILURE":
            response.update(
                {"error": str(task.result), "message": "Error al ejecutar la tarea"}
            )
        elif task.state in ["PENDING", "STARTED"]:
            response["message"] = "Tarea en proceso"

        return Response(response)
