import uuid
from storage.minio import MinioStorage
from rest_framework.exceptions import APIException, ValidationError
import pymupdf
import qrcode
from django.conf import settings


def upload_pdf_to_minio(file_obj, bucket_name="private"):
    """
    Upload a PDF file to MinIO storage with proper validation.

    Args:
        file_obj: The uploaded file object
        bucket_name (str): MinIO bucket name (default: "private")

    Returns:
        tuple: (fid, object_name)

    Raises:
        ValidationError: If file validation fails
        APIException: If upload fails
    """
    try:
        # Validate file size (max 10MB for PDFs)
        max_size = 10 * 1024 * 1024  # 10 megabytes
        if file_obj.size > max_size:
            raise ValidationError("El tamaño máximo permitido para el PDF es 10MB.")

        # Validate file type
        if file_obj.content_type not in ["application/pdf"]:
            raise ValidationError("Solo se permiten archivos PDF.")

        # Validate file extension
        if not file_obj.name.lower().endswith(".pdf"):
            raise ValidationError("Solo se permiten archivos con extensión .pdf")

        # Generate unique filename
        fid = uuid.uuid4()
        object_name = f"{fid}/{file_obj.name.lower()}"

        # Upload to MinIO
        minio = MinioStorage()
        minio.upload(
            bucket_name=bucket_name,
            object_name=object_name,
            data=file_obj,
            length=file_obj.size,
            content_type=file_obj.content_type,
        )

        return fid, object_name
    except (ValidationError, Exception) as e:
        if isinstance(e, ValidationError):
            raise e
        raise APIException(f"Error al subir el archivo: {str(e)}")


def add_csv_mark_to_pdf(pdf_file_response, cid):
    """
    Add CSV data to an existing PDF file.

    Args:
        pdf_file_response: The PDF file response from MinIO
        cid: The unique identifier for the certificate

    Returns:
        The modified PDF file as bytes
    """
    import io

    # Generate a URL for put in the pdf
    csv_url = f"{settings.APP_HOST}/credencial/{cid}"

    # Make a QR with the csv_url
    qr = qrcode.QRCode(version=1, box_size=10, border=4)
    qr.add_data(csv_url)
    qr.make()
    qr_img = qr.make_image(fill_color="#022d58", back_color="white")

    # Read the PDF data from MinIO response
    pdf_data = pdf_file_response.read()

    # Open the PDF document
    pdf_doc = pymupdf.open(stream=pdf_data, filetype="pdf")

    # Get the last page to add QR code
    page = pdf_doc[-1]

    # Convert QR image to bytes
    qr_bytes = io.BytesIO()
    qr_img.save(qr_bytes, "PNG")
    qr_bytes.seek(0)

    # Add QR code image to the page
    # Position it in the bottom right corner
    page_rect = page.rect
    qr_size = 100
    qr_rect = pymupdf.Rect(
        page_rect.width - qr_size - 15,  # x0: 10px from right edge
        page_rect.height - qr_size - 10,  # y0: 10px from bottom
        page_rect.width - 15,  # x1
        page_rect.height - 10,  # y1
    )

    # Insert image using the correct PyMuPDF method
    page.insert_image(qr_rect, stream=qr_bytes.getvalue())

    # Add the verification URL as text below QR
    text_point = pymupdf.Point(page_rect.width - 680, page_rect.height - 10)
    page.insert_text(
        text_point, f"ID de Certificado: {cid}. Verifica en {csv_url}", fontsize=8
    )

    # Save the modified PDF to bytes
    output_buffer = io.BytesIO()
    pdf_doc.save(output_buffer)
    pdf_doc.close()

    output_buffer.seek(0)
    return output_buffer


def replace_file_minio(bucket_name, object_name, pdf_buffer):
    """
    Replace a file in MinIO with new content.

    Args:
        bucket_name: The bucket name
        object_name: The object name
        pdf_buffer: BytesIO buffer containing the PDF content
    """
    minio = MinioStorage()

    # Get the size of the buffer
    pdf_buffer.seek(0, 2)  # Seek to end
    buffer_size = pdf_buffer.tell()
    pdf_buffer.seek(0)  # Reset to beginning

    minio.upload(
        bucket_name=bucket_name,
        object_name=object_name,
        data=pdf_buffer,
        length=buffer_size,
        content_type="application/pdf",
    )
