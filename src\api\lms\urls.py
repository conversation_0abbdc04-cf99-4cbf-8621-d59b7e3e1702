from rest_framework import routers
from api.lms.views.enrollment import LmsEnrollmentViewSet
from api.lms.views.credential import LmsCredentialViewSet
from api.lms.views.offering import LmsOfferingViewSet
from api.lms.views.team_channel import TeamChannelViewSet
from api.lms.views.certificate import CertificateViewSet
from api.lms.views.task import TaskViewSet

router = routers.DefaultRouter(trailing_slash=False)

router.register(
    r"enrollments",
    LmsEnrollmentViewSet,
    basename="lms-enrollment",
)

router.register(
    r"credentials",
    LmsCredentialViewSet,
    basename="lms-credential",
)

router.register(
    r"certificates",
    CertificateViewSet,
    basename="certificate",
)

router.register(
    r"tasks",
    TaskViewSet,
    basename="task",
)

router.register(
    r"offerings",
    LmsOfferingViewSet,
    basename="lms-offering",
)

router.register(
    r"team-channels",
    TeamChannelViewSet,
    basename="team-channel",
)

urlpatterns = router.urls
