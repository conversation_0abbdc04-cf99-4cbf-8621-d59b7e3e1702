from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from api.lms.serializers.certificate import CertificateGenerationSerializer
from api.lms.tasks.generate_certificate import generate_student_certificate, TASK_STATES
from celery.result import AsyncResult


class CertificateViewSet(viewsets.ViewSet):
    """
    ViewSet para manejar la generación de certificados.
    """

    @action(detail=False, methods=["post"])
    def generate(self, request):
        """
        Inicia la generación asíncrona de un certificado.
        """
        serializer = CertificateGenerationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Iniciar tarea Celery
        task = generate_student_certificate.delay(
            serializer.validated_data["enrollment_id"]
        )

        return Response(
            {"task_id": task.id, "status": "PENDING", "message": TASK_STATES["PENDING"]}
        )

    @action(detail=False, methods=["get"])
    def task_status(self, request):
        """
        Obtiene el estado de una tarea de generación de certificado.
        """
        task_id = request.query_params.get("task_id")
        if not task_id:
            return Response({"error": "Se requiere el parámetro task_id"}, status=400)

        # Obtener resultado de la tarea
        task_result = AsyncResult(task_id)

        # Preparar respuesta
        response = {
            "task_id": task_id,
            "status": task_result.state,
            "message": TASK_STATES.get(task_result.state, "Estado desconocido"),
        }

        # Si la tarea falló, incluir el error
        if task_result.failed():
            response["error"] = str(task_result.result)

        # Si la tarea se completó, incluir el resultado
        elif task_result.successful():
            response["result"] = task_result.result

        return Response(response)
