import logging
from celery import shared_task
from storage.minio import MinioStorage
from core.models import StudentEnrollment, File, Credential
from api.lms.services.file import add_csv_mark_to_pdf, replace_file_minio

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def process_certificate_attachment(self, enrollment_id, file_id):
    """
    Process certificate attachment for an enrollment.

    Args:
        enrollment_id (str): UUID of the enrollment
        file_id (str): UUID of the file
    """
    try:
        # Get the enrollment instance
        enrollment = StudentEnrollment.objects.get(eid=enrollment_id)
        # Get the file instance
        file = File.objects.get(fid=file_id)

        # Initialize MinIO storage
        storage = MinioStorage()

        try:
            self.update_state(
                state="PROGRESS",
                meta={"message": "Leyendo el archivo subido..."},
            )
            file_data = storage.get_object(
                bucket_name=file.bucket_name,
                object_name=file.object_name,
            )

            self.update_state(
                state="PROGRESS",
                meta={"message": "Generando credencial y código QR..."},
            )

            # Create the Credential object first
            credential = (
                Credential.objects.create()
                if not enrollment.certificate
                else enrollment.certificate
            )

            # Use the credential's cid for the QR code
            updated_pdf_buffer = add_csv_mark_to_pdf(file_data, credential.cid)

            self.update_state(
                state="PROGRESS",
                meta={"message": "Subiendo PDF modificado..."},
            )

            # Replace the modified PDF in MinIO
            replace_file_minio(
                bucket_name=file.bucket_name,
                object_name=file.object_name,
                pdf_buffer=updated_pdf_buffer,
            )
            self.update_state(
                state="PROGRESS",
                meta={"message": "Actualizando base de datos..."},
            )

            # Update the credential with the file reference
            credential.file = file
            credential.save()

            # Update the enrollment with the credential
            enrollment.certificate = credential
            enrollment.certificate_issued = True
            enrollment.save()

            # Mark the file as used
            file.is_used = True
            file.save()

        except Exception as e:
            logger.error(f"Error processing certificate: {e}")
            raise

        logger.info(
            f"Certificate attachment processed successfully for enrollment: {enrollment_id}"
        )

        return {
            "enrollment": enrollment_id,
            "credential": str(credential.cid),
            "message": "Certificado procesado correctamente",
        }

    except StudentEnrollment.DoesNotExist:
        logger.error(f"Enrollment not found: {enrollment_id}")
        raise Exception(f"Enrollment not found: {enrollment_id}")
    except File.DoesNotExist:
        logger.error(f"File not found: {file_id}")
        raise Exception(f"File not found: {file_id}")
    except Exception as e:
        logger.error(f"Error processing certificate attachment: {e}")
        raise
